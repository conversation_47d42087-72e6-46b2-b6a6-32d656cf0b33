<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import LeftColumn from '../components/LeftColumn.vue'
// import RightColumn from '../components/RightColumn.vue'
import DataSheet from '../components/DataSheet.vue'
import { useStates } from '@/store/states'
import axios from 'axios'
import ToastNotification from '../components/common/ToastNotification.vue'
import IndexSelector from '../components/IndexSelector.vue'
import DimensionSelector from '../components/DimensionSelector.vue'
import { handleError } from '@/utils/errorHandler'

const statesStore = useStates()
const { authToken } = storeToRefs(statesStore)

const currentPage = ref('datasheet') // home 或 datasheet - default to datasheet
const router = useRouter()
const navigateTo = (path) => {
  router.push(path)
}

const loading = ref(false)
const progress = ref(0)

const showNotification = ref(false)
const notificationMessage = ref('')
const notificationType = ref('success')
const showToast = (message, type = 'success') => {
  notificationMessage.value = message
  notificationType.value = type
  showNotification.value = true
}
const handleToastClose = () => {
  showNotification.value = false
}

const showIndexSelector = ref(false)
const showDimensionSelector = ref(false)

// Floating window state management with persistence
const isFloatingWindowMinimized = ref(localStorage.getItem('floatingWindowMinimized') === 'true')
const showFloatingWindow = ref(true)

// Watch for changes and persist state
watch(isFloatingWindowMinimized, (newValue) => {
  localStorage.setItem('floatingWindowMinimized', newValue.toString())
})

function handleAddIndex() {
  // Close dimension selector if it's open
  if (showDimensionSelector.value) {
    showDimensionSelector.value = false
  }
  showIndexSelector.value = true
}
function handleCloseIndexSelector() {
  showIndexSelector.value = false
}

function handleAddDimension() {
  // Close index selector if it's open
  if (showIndexSelector.value) {
    showIndexSelector.value = false
  }
  showDimensionSelector.value = true
}
function handleCloseDimensionSelector() {
  showDimensionSelector.value = false
}

function handleIndexSelect(idx) {
  // Find 目标指标 component
  const indicatorsComp = statesStore.states.find((s) => s.componentName === '目标指标')
  if (!indicatorsComp) return
  // Prevent duplicate by checking both ID and name
  if (indicatorsComp.indices.some((i) => i.indexId === idx.id || i.indexName === idx.name)) {
    showToast('该指标已添加', 'error')
    return
  }
  // Store both ID and name for proper API communication
  statesStore.updateComponent(indicatorsComp.componentId, {
    ...indicatorsComp,
    indices: [
      ...indicatorsComp.indices,
      {
        indexId: idx.id,
        indexName: idx.name,
        originalData: idx.originalData, // Store complete metadata
      },
    ],
  })
  // Auto-close IndexSelector after successful selection
  showToast(`指标 "${idx.name}" 已添加`, 'success')
  // showIndexSelector.value = false
}

function handleDimensionSelect(dimension) {
  // Find 目标维度 component
  const dimensionsComp = statesStore.states.find((s) => s.componentName === '目标维度')
  if (!dimensionsComp) return
  // Prevent duplicate by checking both ID and name
  if (
    dimensionsComp.dimensions.some(
      (d) => d.dimensionId === dimension.id || d.dimensionName === dimension.name,
    )
  ) {
    showToast('该维度已添加', 'error')
    return
  }
  // Store both ID and name for proper API communication
  statesStore.updateComponent(dimensionsComp.componentId, {
    ...dimensionsComp,
    dimensions: [
      ...dimensionsComp.dimensions,
      {
        dimensionId: dimension.id,
        dimensionName: dimension.name,
        originalData: dimension.originalData, // Store complete metadata
      },
    ],
  })
  // Auto-close DimensionSelector after successful selection
  showToast(`维度 "${dimension.name}" 已添加`, 'success')
  // showDimensionSelector.value = false
}

// Floating window control functions
function minimizeFloatingWindow() {
  isFloatingWindowMinimized.value = true
}

function expandFloatingWindow() {
  isFloatingWindowMinimized.value = false
}

// Handle file upload success and auto-minimize
function handleFileUploaded(uploadInfo) {
  if (uploadInfo.success) {
    let successMessage = `文件 "${uploadInfo.fileName}" 上传成功`
    if (uploadInfo.table_id) {
      successMessage += `，表格ID: ${uploadInfo.table_id}`
    }
    showToast(successMessage, 'success')

    // Auto-minimize floating window after successful file upload
    setTimeout(() => {
      minimizeFloatingWindow()
    }, 800) // Small delay to let user see the upload completion
  } else {
    // Enhanced error handling for specific error cases
    let errorMessage = `文件上传失败: ${uploadInfo.error || '未知错误'}`

    // Provide more specific guidance for common errors
    if (uploadInfo.error) {
      if (uploadInfo.error.includes('认证失败')) {
        errorMessage = '文件上传失败: 认证失败，请重新登录后再试'
      } else if (uploadInfo.error.includes('请求参数缺失')) {
        errorMessage = '文件上传失败: 请求参数缺失，请稍后重试'
      } else if (uploadInfo.error.includes('网络连接失败')) {
        errorMessage = '文件上传失败: 网络连接失败，请检查网络连接后重试'
      } else if (uploadInfo.error.includes('服务器错误')) {
        errorMessage = '文件上传失败: 服务器暂时不可用，请稍后重试'
      }
    }

    showToast(errorMessage, 'error')
  }
}

const handleDataSheetClick = async () => {
  // 1. Gather states
  const states = statesStore.states

  // Find relevant components
  const indicesComp = states.find((s) => s.componentName === '目标指标')
  const dimensionsComp = states.find((s) => s.componentName === '目标维度')
  const granularityComp = states.find((s) => s.componentName === '粒度')
  const dateComp = states.find((s) => s.componentName === '日期')
  const filterComp = states.find((s) => s.componentName === '筛选')

  // Extract required data - ensure we use ID values for API communication
  const granularityId = granularityComp?.granularityId || ''
  const indices =
    indicesComp?.indices?.map((i) => {
      // Use indexId for API calls (key_id parameter)
      if (typeof i === 'object' && i.indexId) {
        return i.indexId
      }
      // Fallback for legacy data format
      return typeof i === 'string' ? i : i.indexName
    }) || []

  const dimensions =
    dimensionsComp?.dimensions?.map((d) => {
      // Use dimensionId for API calls (key_id parameter)
      if (typeof d === 'object' && d.dimensionId) {
        return d.dimensionId
      }
      // Fallback for legacy data format
      return typeof d === 'string' ? d : d.dimensionName
    }) || []

  const dates = dateComp?.dates || []

  // Transform filter conditions to new API format
  let filter = []
  if (filterComp && Array.isArray(filterComp.filterGroups)) {
    filter = filterComp.filterGroups
      .map((group, groupIndex) => {
        // Transform conditions within each group
        const validConditions = group.conditions.filter(
          (cond) => cond.field && cond.operator && cond.value,
        ) // Only include complete conditions
        const conditions = validConditions.map((cond, condIndex) => ({
          column: cond.field || '', // Use "column" instead of "field"
          operator: cond.operator || '',
          value: cond.value || '',
          logic: condIndex === validConditions.length - 1 ? '' : cond.logic || 'and', // Empty string for last condition in group
        }))

        // Only include groups that have valid conditions
        if (conditions.length === 0) return null

        return {
          conditions: conditions,
          logic: groupIndex === filterComp.filterGroups.length - 1 ? '' : group.groupLogic || 'and', // Empty string for last group
        }
      })
      .filter((group) => group !== null) // Remove empty groups
  }

  // --- Required field validation ---
  let missingFields = []
  if (!granularityId) missingFields.push('粒度')
  if (!indices || indices.length === 0) missingFields.push('指标')

  // Check for unfinished filter conditions
  if (filterComp && Array.isArray(filterComp.filterGroups)) {
    const unfinished = filterComp.filterGroups.some((group) =>
      group.conditions.some((cond) => {
        // If any part is filled but not all, it's unfinished
        const filledCount = [cond.field, cond.operator, cond.value].filter((v) => {
          if (Array.isArray(v)) return v.some((val) => val && val.toString().trim() !== '')
          return v && v.toString().trim() !== ''
        }).length
        return filledCount > 0 && filledCount < 3
      }),
    )
    if (unfinished) missingFields.push('筛选条件未完整填写')
  }

  if (missingFields.length > 0) {
    showToast('请完善以下必填项: ' + missingFields.join('、'), 'error')
    return
  }

  // 2. Construct API payload using key_id parameter names for ID values
  const payload = {
    task_id: statesStore.taskId,
    key: granularityId, // Granularity ID
    indices: indices, // Array of indicator IDs
    dimensions: dimensions.length > 0 ? dimensions : undefined, // Array of dimension IDs
    dates: dates.length > 0 ? dates : undefined,
    filter: filter.length > 0 ? filter : undefined,
  }

  // Remove undefined fields from payload
  Object.keys(payload).forEach((key) => {
    if (payload[key] === undefined) {
      delete payload[key]
    }
  })

  console.log('API Payload:', payload)

  // 3. Make API call
  loading.value = true
  progress.value = 0

  // Simulate progress bar during API call
  const progressInterval = setInterval(() => {
    if (progress.value < 90) {
      progress.value += Math.floor(Math.random() * 8) + 2 // random step
    } else if (progress.value < 98) {
      progress.value += 1
    }
    if (progress.value > 98) progress.value = 98
  }, 200)

  try {
    const resp = await axios.post('/api/v1/config/fetch_data', payload, {
      headers: {
        token: localStorage.getItem('token'),
        'Content-Type': 'application/json',
      },
    })
    console.log('API Response:', resp)

    // 4. Handle API response
    const responseData = resp.data

    // Check for API errors
    if (responseData.error_code !== 0) {
      let errorMessage = '数据获取失败'

      switch (responseData.error_code) {
        case 301:
          errorMessage = '认证失败，请重新登录'
          break
        case 302:
          errorMessage = '请求参数缺失，请检查必填项'
          break
        default:
          errorMessage = responseData.message || '未知错误'
      }

      showToast(errorMessage, 'error')
      return
    }

    // 5. Process successful response and store in Pinia table state
    const { table_id, table_info, table_data } = responseData.data || {}
    const tableComp = states.find((s) => s.componentName === 'table')

    if (tableComp && table_data && typeof table_data === 'object') {
      // Build columns from enhanced table_info metadata structure
      let columns = []
      if (table_info && typeof table_info === 'object') {
        // Check if table_info has the enhanced nested structure
        if (table_info.output_df_dict && typeof table_info.output_df_dict === 'object') {
          // Enhanced structure: extract column metadata from output_df_dict
          columns = Object.keys(table_info.output_df_dict).map((columnName) => {
            const columnMeta = table_info.output_df_dict[columnName] || {}
            return {
              field: columnName,
              title: columnName, // Use column name as display title
              dtype: columnMeta.dtype || 'unknown',
              ftype: columnMeta.ftype || 'unknown', // Keep for backward compatibility
              missing_count: columnMeta.missing_count || 0,
              missing_rate: columnMeta.missing_rate || 0,
              dirty_count: columnMeta.dirty_count || 0,
              dirty_rate: columnMeta.dirty_rate || 0,
              column_index: columnMeta.column_index || 0,
              outlier_count: columnMeta.outlier_count || 0, // Keep for backward compatibility
              unique_values: columnMeta.unique_values || [], // Keep for backward compatibility
            }
          })
        } else {
          // Legacy structure: treat table_info as flat object with column metadata
          columns = Object.keys(table_info).map((columnName) => {
            const columnMeta = table_info[columnName] || {}
            return {
              field: columnName,
              title: columnName, // Use column name as display title
              dtype: columnMeta.dtype || 'unknown',
              ftype: columnMeta.ftype || 'unknown',
              missing_count: columnMeta.missing_count || 0,
              outlier_count: columnMeta.outlier_count || 0,
              unique_values: columnMeta.unique_values || [],
            }
          })
        }
      } else {
        // Fallback: infer columns from table_data keys if table_info is not available
        columns = Object.keys(table_data).map((key) => ({
          field: key,
          title: key,
          dtype: 'unknown',
          ftype: 'unknown',
          missing_count: 0,
          outlier_count: 0,
          unique_values: [],
        }))
      }

      // Transform table_data from column-based to row-based format
      const columnNames = Object.keys(table_data)
      const rowCount = columnNames.length > 0 ? table_data[columnNames[0]]?.length || 0 : 0
      const rows = []

      for (let i = 0; i < rowCount; i++) {
        const row = {}
        columnNames.forEach((columnName) => {
          // Explicitly handle zero values - don't use || null which would convert 0 to null
          const value = table_data[columnName]?.[i]
          row[columnName] = value !== undefined ? value : null
        })
        rows.push(row)
      }

      // Store in Pinia with enhanced metadata
      const tableName = table_id || 'fetched_data'
      const newSheetData = { ...(tableComp.sheetData || {}) }

      newSheetData[tableName] = {
        name: tableName,
        columns,
        rows,
        table_id,
        table_info, // Store complete table_info metadata
        table_data, // Store original column-based data for reference
        fetchTimestamp: new Date().toISOString(),
      }

      statesStore.updateComponent(tableComp.componentId, {
        ...tableComp,
        activeSheet: tableName,
        sheetData: newSheetData,
      })

      showToast('数据获取成功', 'success')
    } else {
      showToast('数据格式异常，请联系管理员', 'error')
      return
    }

    console.log('Current states:', states)
    currentPage.value = 'datasheet'

    // Auto-minimize floating window after successful data load
    setTimeout(() => {
      minimizeFloatingWindow()
    }, 800) // Small delay to let user see the completion
  } catch (e) {
    console.error('API Error:', e)
    let errorMessage = '数据请求失败'

    if (e.response) {
      // Server responded with error status
      const status = e.response.status
      const data = e.response.data

      if (status === 401) {
        errorMessage = '认证失败，请重新登录'
      } else if (status === 400) {
        errorMessage = '请求参数错误'
      } else if (data && data.message) {
        errorMessage = data.message
      } else {
        errorMessage = `服务器错误 (${status})`
      }
    } else if (e.request) {
      // Network error
      errorMessage = '网络连接失败，请检查网络'
    } else {
      errorMessage = e.message || '未知错误'
    }

    // Use the new error handler to classify and display errors appropriately
    const retryAction = () => fetchData() // Allow retry for system errors
    handleError(e, errorMessage, retryAction, showToast)
  } finally {
    clearInterval(progressInterval)
    progress.value = 100
    setTimeout(() => {
      loading.value = false
      progress.value = 0
    }, 10000)
  }
}

onMounted(() => {
  // Set page name for guided analysis
  statesStore.$patch({ pageName: '引导式' })

  // // Initialize task name with default for guided analysis
  // if (!statesStore.taskName || statesStore.taskName === '' || statesStore.taskName === '未命名') {
  //   statesStore.setTaskName('个贷催收场景经营分析')
  // }
})

// Animated dots for 加载中 ...
const dotCount = ref(0)
let dotTimer = null

const isProgressOverText = computed(() => progress.value >= 50)
watch(loading, (val) => {
  if (val) {
    dotCount.value = 0
    dotTimer = setInterval(() => {
      dotCount.value = (dotCount.value + 1) % 4
    }, 400)
  } else {
    clearInterval(dotTimer)
    dotTimer = null
    dotCount.value = 0
  }
})
</script>

<template>
  <ToastNotification
    :show="showNotification"
    :message="notificationMessage"
    :type="notificationType"
    @close="handleToastClose"
  />
  <!-- Main content: DataSheet as default -->
  <div class="datasheet-container">
    <DataSheet key="datasheet-view" @back="showFloatingWindow = true" />
  </div>

  <!-- Floating Window Overlay -->
  <div
    v-if="showFloatingWindow"
    class="floating-window-overlay"
    :class="{ minimized: isFloatingWindowMinimized }"
  >
    <!-- Floating Window -->
    <div class="floating-window" :class="{ 'slide-out': isFloatingWindowMinimized }">
      <div class="floating-window-header">
        <span class="floating-window-title">取数配置</span>
        <div class="header-buttons">
          <button class="minimize-btn" @click="minimizeFloatingWindow" title="最小化">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M18 12H6" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
            </svg>
          </button>
        </div>
      </div>
      <div class="floating-window-content">
        <div class="left-column">
          <LeftColumn
            @goto-datasheet="() => {}"
            @add-index="handleAddIndex"
            @add-dimension="handleAddDimension"
            @file-uploaded="handleFileUploaded"
          />
        </div>
        <div class="nav-buttons">
          <button
            @click="handleDataSheetClick"
            :class="{ active: currentPage === 'datasheet' }"
            :disabled="loading"
          >
            <template v-if="loading">
              <span class="progress-bar-wrapper">
                <span class="progress-bar-fill" :style="{ width: progress + '%' }"></span>
                <span
                  class="progress-bar-text"
                  :class="{ 'progress-bar-text-white': isProgressOverText }"
                >
                  加载中
                  <span class="dot-seq">
                    <span v-for="i in 3" :key="i" :class="['dot', { 'dot-active': dotCount >= i }]"
                      >.</span
                    >
                  </span>
                </span>
              </span>
            </template>
            <span v-else>一键取数</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- IndexSelector Floating Window -->
  <div
    v-if="showIndexSelector && showFloatingWindow"
    class="index-selector-floating-window"
    :class="{ 'slide-out': isFloatingWindowMinimized }"
  >
    <div class="index-selector-window">
      <div class="index-selector-header">
        <span class="index-selector-title">指标选择</span>
        <button class="index-selector-close-btn" @click="handleCloseIndexSelector" title="关闭">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
      <div class="index-selector-content">
        <IndexSelector @close="handleCloseIndexSelector" @select="handleIndexSelect" />
      </div>
    </div>
  </div>

  <!-- DimensionSelector Floating Window -->
  <div
    v-if="showDimensionSelector && showFloatingWindow"
    class="dimension-selector-floating-window"
    :class="{ 'slide-out': isFloatingWindowMinimized }"
  >
    <div class="dimension-selector-window">
      <div class="dimension-selector-header">
        <span class="dimension-selector-title">维度选择</span>
        <button
          class="dimension-selector-close-btn"
          @click="handleCloseDimensionSelector"
          title="关闭"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
      <div class="dimension-selector-content">
        <DimensionSelector @close="handleCloseDimensionSelector" @select="handleDimensionSelect" />
      </div>
    </div>
  </div>

  <!-- Expand Button (shown when minimized) -->
  <button
    v-if="showFloatingWindow && isFloatingWindowMinimized"
    class="expand-btn"
    @click="expandFloatingWindow"
    title="展开配置面板"
  >
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
      <path
        d="M9 18l6-6-6-6"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <span>取数配置</span>
  </button>
</template>

<style scoped>
/* Main datasheet container */
.datasheet-container {
  width: 100vw;
  height: 81vh;
  position: relative;
}

/* Floating window overlay */
.floating-window-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 1000;
}

.floating-window-overlay.minimized {
  pointer-events: none;
}

/* Floating window */
.floating-window {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 530px;
  max-width: calc(100vw - 40px);
  height: calc(100vh - 40px);
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  transform: translateX(0);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-window.slide-out {
  transform: translateX(-620px);
}

/* Floating window header */
.floating-window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px 16px 0 0;
  backdrop-filter: blur(5px);
  transition: all 0.2s ease;
}

.floating-window-header:hover {
  background: rgba(255, 255, 255, 0.95);
}

.floating-window-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  transition: color 0.2s ease;
}

.header-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.minimize-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.minimize-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(25, 118, 210, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.minimize-btn:hover::before {
  width: 100%;
  height: 100%;
}

.minimize-btn:hover {
  background: rgba(25, 118, 210, 0.1);
  color: #1976d2;
  transform: scale(1.05);
}

.minimize-btn:active {
  transform: scale(0.95);
}

/* Floating window content */
.floating-window-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.left-column {
  flex: 1;
  overflow-y: auto;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  margin: 0 20px;
}
/* Navigation buttons in floating window */
.nav-buttons {
  padding: 16px 20px 20px 20px;
  background: transparent;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.nav-buttons button {
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(25, 118, 210, 0.2);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  font-weight: 500;
  color: #1976d2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
  min-height: 44px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.nav-buttons button:hover {
  background: #1976d2;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

.nav-buttons button.active {
  background: #42b983;
  color: white;
  border-color: #42b983;
  box-shadow: 0 4px 12px rgba(66, 185, 131, 0.2);
}

/* Expand button */
.expand-btn {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 50px;
  height: 100px;
  background: rgba(25, 118, 210, 0.95);
  border: none;
  border-radius: 0 12px 12px 0;
  color: white;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 2px 0 12px rgba(25, 118, 210, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1001;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  animation: slideInFromLeftEdge 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.expand-btn:hover {
  background: rgba(25, 118, 210, 1);
  width: 70px;
  box-shadow: 4px 0 16px rgba(25, 118, 210, 0.4);
  transform: translateY(-50%) translateX(2px);
}

.expand-btn svg {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.expand-btn:hover svg {
  transform: rotate(0deg) scale(1.1);
}

@keyframes slideInFromLeftEdge {
  0% {
    transform: translateY(-50%) translateX(-60px);
    opacity: 0;
  }
  100% {
    transform: translateY(-50%) translateX(0);
    opacity: 1;
  }
}

/* Subtle pulse animation for expand button */
.expand-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0 12px 12px 0;
  animation: expandButtonPulse 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes expandButtonPulse {
  0%,
  100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* IndexSelector Floating Window */
.index-selector-floating-window {
  position: fixed;
  top: 20px;
  left: 680px; /* Position to the right of main floating window (420px + 20px gap) */
  width: calc((100vw - 620px) * 0.9); /* 90% of remaining viewport space */
  min-width: 400px; /* Ensure minimum usable width */
  max-width: calc(100vw - 660px); /* Ensure minimum 20px right margin */
  height: calc(100vh - 40px);
  pointer-events: none;
  z-index: 1001;
  transform: translateX(0);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.index-selector-floating-window.slide-out {
  /* Slide completely off-screen to the left: -(left position + window width) */
  transform: translateX(calc(-1 * (680px + (100vw - 620px) * 0.9)));
}

.index-selector-window {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  animation: slideInFromRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(calc(100vw - 440px)); /* Start from right edge of viewport */
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive slide-in animations */
@keyframes slideInFromRightMedium {
  0% {
    transform: translateX(calc(100vw - 400px)); /* Start from right edge for 1024px screens */
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.index-selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px 16px 0 0;
  backdrop-filter: blur(5px);
  transition: all 0.2s ease;
}

.index-selector-header:hover {
  background: rgba(255, 255, 255, 0.95);
}

.index-selector-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  transition: color 0.2s ease;
}

.index-selector-close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.index-selector-close-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(25, 118, 210, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.index-selector-close-btn:hover::before {
  width: 100%;
  height: 100%;
}

.index-selector-close-btn:hover {
  background: rgba(25, 118, 210, 0.1);
  color: #1976d2;
  transform: scale(1.05);
}

.index-selector-close-btn:active {
  transform: scale(0.95);
}

.index-selector-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
}

/* DimensionSelector Floating Window */
.dimension-selector-floating-window {
  position: fixed;
  top: 20px;
  left: 680px; /* Position to the right of main floating window (620px + 60px gap) */
  width: calc((100vw - 620px) * 0.9); /* 90% of remaining viewport space */
  min-width: 400px; /* Ensure minimum usable width */
  max-width: calc(100vw - 660px); /* Ensure minimum 20px right margin */
  height: calc(100vh - 40px);
  pointer-events: none;
  z-index: 1002; /* Higher than index selector */
  transform: translateX(0);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dimension-selector-floating-window.slide-out {
  /* Slide completely off-screen to the left: -(left position + window width) */
  transform: translateX(calc(-1 * (680px + (100vw - 620px) * 0.9)));
}

.dimension-selector-window {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  animation: slideInFromRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.dimension-selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px 16px 0 0;
}

.dimension-selector-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
}

.dimension-selector-close-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dimension-selector-close-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #1976d2;
}

.dimension-selector-close-btn:active {
  transform: scale(0.95);
}

.dimension-selector-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
}
/* Responsive design for floating windows */
@media (max-width: 1200px) {
  .index-selector-floating-window {
    left: 440px; /* Maintain consistent positioning */
    width: calc((100vw - 620px) * 0.9); /* Keep 90% calculation with updated base */
    min-width: 350px; /* Smaller minimum for medium screens */
    max-width: calc(100vw - 660px); /* Ensure minimum 20px right margin */
  }

  .index-selector-floating-window.slide-out {
    /* Slide completely off-screen to the left: -(left position + window width) */
    transform: translateX(calc(-1 * (440px + (100vw - 620px) * 0.9)));
  }

  .dimension-selector-floating-window {
    left: 480px; /* Position after index selector */
    width: calc((100vw - 660px) * 0.9);
    min-width: 350px;
    max-width: calc(100vw - 700px);
  }

  .dimension-selector-floating-window.slide-out {
    transform: translateX(calc(-1 * (480px + (100vw - 660px) * 0.9)));
  }
}

@media (max-width: 1024px) {
  .floating-window {
    width: 360px; /* Slightly smaller main window */
  }

  .index-selector-floating-window {
    left: 400px; /* Adjust for smaller main window (360px + 20px margin + 20px gap) */
    width: calc(
      (100vw - 580px) * 0.9
    ); /* 90% of remaining space after 580px (360px + 20px + 20px + 180px buffer) */
    min-width: 300px; /* Smaller minimum for smaller screens */
    max-width: calc(100vw - 420px); /* Ensure minimum 20px right margin */
  }

  .index-selector-window {
    animation: slideInFromRightMedium 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .index-selector-floating-window.slide-out {
    /* Slide completely off-screen to the left: -(left position + window width) */
    transform: translateX(calc(-1 * (400px + (100vw - 580px) * 0.9)));
  }

  .dimension-selector-floating-window {
    left: 440px; /* Position after index selector */
    width: calc((100vw - 620px) * 0.9);
    min-width: 300px;
    max-width: calc(100vw - 460px);
  }

  .dimension-selector-window {
    animation: slideInFromRightMedium 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .dimension-selector-floating-window.slide-out {
    transform: translateX(calc(-1 * (440px + (100vw - 620px) * 0.9)));
  }
}

@media (max-width: 900px) {
  /* Stack IndexSelector below main window on smaller screens */
  .index-selector-floating-window {
    top: calc(50vh + 10px);
    left: 20px;
    width: calc(100vw - 40px); /* Full width minus margins for stacked layout */
    height: calc(50vh - 30px);
    max-width: none; /* Remove max-width constraint for stacked layout */
  }

  .index-selector-floating-window.slide-out {
    transform: translateY(calc(50vh + 10px)); /* Slide down instead of left */
  }

  .floating-window {
    height: calc(50vh - 30px);
  }

  /* Stack DimensionSelector below IndexSelector on smaller screens */
  .dimension-selector-floating-window {
    top: calc(75vh + 10px);
    left: 20px;
    width: calc(100vw - 40px);
    height: calc(25vh - 30px);
    max-width: none;
  }

  .dimension-selector-floating-window.slide-out {
    transform: translateY(calc(25vh + 10px));
  }
}

@media (max-width: 768px) {
  .floating-window {
    width: calc(100vw - 20px); /* Full width minus margins */
    left: 10px;
    top: 10px;
    height: calc(50vh - 20px);
    max-width: none;
  }

  .floating-window.slide-out {
    transform: translateX(calc(-100vw + 10px));
  }

  .index-selector-floating-window {
    top: calc(50vh + 10px);
    left: 10px;
    width: calc(100vw - 20px); /* Full width minus margins for mobile */
    height: calc(50vh - 30px);
    max-width: none; /* Remove max-width constraint for mobile */
  }

  .index-selector-floating-window.slide-out {
    transform: translateY(calc(50vh + 10px)); /* Slide down for mobile */
  }

  .dimension-selector-floating-window {
    top: calc(75vh + 10px);
    left: 10px;
    width: calc(100vw - 20px);
    height: calc(25vh - 30px);
    max-width: none;
  }

  .dimension-selector-floating-window.slide-out {
    transform: translateY(calc(25vh + 10px));
  }

  .expand-btn {
    width: 50px;
    height: 100px;
    font-size: 0.75rem;
  }

  .expand-btn:hover {
    width: 55px;
  }
}

@media (max-width: 1024px) {
  .floating-window {
    width: 380px;
  }
}

@media (max-width: 480px) {
  .floating-window-header {
    padding: 12px 16px;
  }

  .floating-window-title {
    font-size: 1rem;
  }

  .minimize-btn {
    width: 28px;
    height: 28px;
  }

  .nav-buttons {
    padding: 12px 16px 16px 16px;
  }

  .left-column {
    margin: 0 16px;
  }

  /* IndexSelector responsive adjustments */
  .index-selector-header {
    padding: 12px 16px;
  }

  .index-selector-title {
    font-size: 1rem;
  }

  .index-selector-close-btn {
    width: 28px;
    height: 28px;
  }

  .index-selector-content .selector-body {
    padding: 16px;
    gap: 16px;
    flex-direction: column;
  }

  .index-selector-content .category-levels {
    min-width: auto;
    max-width: none;
  }
}
/* Button states and animations */
.nav-buttons button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.5) !important;
  color: #999 !important;
}

.nav-buttons button[disabled]:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
}

/* Progress bar styles for floating window */
.progress-bar-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  pointer-events: none;
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #1976d2 0%, #42b983 100%);
  border-radius: 10px;
  transition: width 0.3s ease;
  z-index: 0;
}

.progress-bar-text {
  position: relative;
  z-index: 2;
  color: #1976d2;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  white-space: nowrap;
  text-shadow:
    0 0 4px rgba(255, 255, 255, 0.8),
    0 1px 2px rgba(0, 0, 0, 0.1);
  transition:
    color 0.2s,
    text-shadow 0.2s;
}

.progress-bar-text-white {
  color: #fff !important;
  text-shadow:
    0 0 6px rgba(25, 118, 210, 0.5),
    0 1px 2px rgba(0, 0, 0, 0.2);
}

.dot-seq {
  display: inline-block;
  margin-left: 4px;
}

.dot {
  display: inline-block;
  opacity: 0.4;
  transform: translateY(0);
  transition:
    opacity 0.2s,
    transform 0.2s;
  font-size: 1.1em;
}

.dot-active {
  opacity: 1;
  animation: dot-bounce 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55) alternate;
}

@keyframes dot-bounce {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
